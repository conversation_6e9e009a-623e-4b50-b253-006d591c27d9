import os
from django.http import StreamingHttpResponse
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from accounts.permissions import IsClient
from accounts.models import Folio, Document, Client
from accounts.serializers import DashboardFolioSerializer, DashboardDocumentSerializer
from .performance_services import get_overall_fund_performance_chart_data, get_client_nav_performance_charts
from django.db.models import Q
import calendar
from datetime import date
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator


class ClientDashboardView(APIView):
    """
    Provides filtered and real-time data for the client dashboard, including:
    - Active folios for the client.
    - Client and Company documents for the CURRENT MONTH.
    - Fund vs. Benchmark performance data (Column Chart).
    - Client's monthly NAV performance data (Line Charts).
    """
    permission_classes = [IsAuthenticated, IsClient]

    def get(self, request):
        try:
            # Ensure the user has a client profile
            client = request.user.client
        except Client.DoesNotExist:
            return Response({"error": "Client profile not found for this user."}, status=404)

        # --- Data Fetching and Filtering ---

        current_date = date.today()
        current_year = current_date.year
        current_month = current_date.month

        # 1. Get ONLY active folios for the client
        folio_serializer = DashboardFolioSerializer(client, context={'request': request})

        # 2. Get company-level documents for the CURRENT MONTH
        company_documents = Document.objects.filter(
            document_type='Company',
            year=current_year,
            month=current_month
        )
        company_doc_serializer = DashboardDocumentSerializer(company_documents, many=True, context={'request': request})

        # 3. Get performance data for the column chart from the new service
        fund_performance_data = get_overall_fund_performance_chart_data()

        # 4. Get client-specific NAV performance for the line charts
        client_nav_charts = get_client_nav_performance_charts(request.user)


        # --- Construct the Final Response Payload ---

        response_data = {
            "message": f"Welcome, {request.user.first_name or request.user.username}",
            "folio_data": folio_serializer.data,
            "company_documents_current_month": company_doc_serializer.data,
            "performance_chart_data": fund_performance_data, # This data is already a list of dicts
            "client_line_charts_data": client_nav_charts # This data is also ready for JSON
        }

        return Response(response_data)

class ClientDocumentView(APIView):
    """
    Provides a structured view of all documents accessible to a client,
    organized by year, quarter, and month for easy navigation.
    """
    permission_classes = [IsAuthenticated, IsClient]

    def get(self, request, *args, **kwargs):
        try:
            client = request.user.client
        except Client.DoesNotExist:
            return Response({"error": "Client profile not found for this user."}, status=status.HTTP_404_NOT_FOUND)

        # 1. Fetch all relevant documents in a single, optimized query
        documents = Document.objects.filter(
            Q(client=client) | Q(document_type='Company')
        ).select_related('client', 'folio__class_name').order_by('-year', '-month').distinct()

        # 2. Structure the data into the format expected by the frontend
        company_documents = {}
        client_documents = {}
        
        serializer = DashboardDocumentSerializer(documents, many=True, context={'request': request})

        for doc_data in serializer.data:
            year = doc_data.get('year')
            month = doc_data.get('month')
            doc_type = doc_data.get('document_type')

            if not year or not month:
                continue # Skip documents without a year/month
            
            year_key = str(year)
            quarter_key = str((month - 1) // 3 + 1)  # Remove 'Q' prefix for consistency
            month_key = calendar.month_name[month]

            if doc_type == 'Company':
                # Structure company documents by year/quarter/month
                year_node = company_documents.setdefault(year_key, {})
                quarter_node = year_node.setdefault(quarter_key, {})
                month_node = quarter_node.setdefault(month_key, [])
                month_node.append(doc_data)
            else:
                # Structure client documents by class/year
                class_name = doc_data.get('class_name')
                if class_name:
                    class_key = str(class_name)
                    class_node = client_documents.setdefault(class_key, {})
                    year_node = class_node.setdefault(year_key, [])
                    year_node.append(doc_data)

        structured_data = {
            "company_documents": company_documents,
            "client_documents": client_documents
        }

        return Response(structured_data)

@method_decorator(ratelimit(key='user_or_ip', rate='30/m', block=True), name='dispatch')
class ClientDocumentDownloadView(APIView):
    """
    A view for clients to download documents with specific permissions.
    - Uses the same file handling as DownloadDocumentView.
    - Allows download of any 'Company' document.
    - Restricts 'Client' document downloads to the client's own folios.
    """
    permission_classes = [IsAuthenticated, IsClient] # Ensures the user is logged in.

    def get(self, request, doc_id):
        try:
            document = Document.objects.get(id=doc_id)
        except Document.DoesNotExist:
            return Response({"detail": "Document not found."}, status=status.HTTP_404_NOT_FOUND)

        # --- Authorization Check for 'Client' Documents ---
        if document.document_type == 'Client':
            try:
                # Check if the document's folio belongs to the requesting user's client
                if not document.folio or document.folio.client.user != request.user:
                    return Response(
                        {"detail": "You do not have permission to access this document."},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except AttributeError:
                return Response({"detail": "Document has no associated folio."}, status=status.HTTP_400_BAD_REQUEST)

        # --- Same file download logic as staff DownloadDocumentView ---
        # Logic to generate a descriptive filename
        new_filename = os.path.basename(document.uploaded_file.name)

        # Open the file from S3 storage (or other storage) in binary read mode
        file_iterator = document.uploaded_file.open('rb')

        # Create a memory-efficient streaming response
        response = StreamingHttpResponse(file_iterator, content_type='application/octet-stream')

        # Set headers to prompt download with the NEW, descriptive filename
        response['Content-Disposition'] = f'attachment; filename="{new_filename}"'
        response['Content-Length'] = document.uploaded_file.size

        return response
