"""
Local filesystem storage classes used in place of S3-backed storage.
These keep the same class names so existing code that references
`amportal_backend.storage.PrivateMediaStorage` / `PublicStaticStorage`
continues to work, but files will be stored under the project `files/`
directory (or subfolders) as configured in `settings.MEDIA_ROOT`.
"""

from django.core.files.storage import FileSystemStorage
from django.conf import settings
import os


class PrivateMediaStorage(FileSystemStorage):
    """Store private media files on local filesystem under MEDIA_ROOT.

    This is a minimal replacement for the S3-backed private storage used
    previously. It does not provide signed URLs; instead, files are
    served directly via MEDIA_URL in development or by a front-facing
    server in production.
    """

    def __init__(self, *args, **kwargs):
        # settings.MEDIA_ROOT should point to <project_root>/files
        location = getattr(settings, 'MEDIA_ROOT', os.path.join(str(settings.BASE_DIR), 'files'))
        base_url = getattr(settings, 'MEDIA_URL', '/files/')
        super().__init__(location=location, base_url=base_url)


class PublicStaticStorage(FileSystemStorage):
    """Store public static files under a 'static' subdirectory inside files/.

    Existing code that referenced the S3-based public static storage will
    continue to work but assets will be placed under files/static.
    """

    def __init__(self, *args, **kwargs):
        location = os.path.join(str(settings.BASE_DIR), 'files', 'static')
        base_url = getattr(settings, 'STATIC_URL', '/static/')
        super().__init__(location=location, base_url=base_url)
