# Byte-compiled / optimized / compiled files
__pycache__/
*.pyc
*.pyd
*.pyo
*.egg-info/
.pytest_cache/

# Environments
gemenv/
venv/
env/
.env/
.venv/
.env

LightsailDefailtKey-ap-south-1.pem

staticfiles/
frontend/build/
frontend/dist/
frontend/*.zip

accounts/token.json

# Database
*.sqlite3
db.sqlite3

# Media and Static Files (if not managed by a CDN or separate storage)
media/
static_collected/

# Configuration files
*.env
local_settings.py  # Or any other file containing sensitive credentials

# OAuth credentials
accounts/credentials.json
accounts/token.json

files/
# IDE-specific files
.idea/  # PyCharm
.vscode/ # VS Code
*.sublime-project
*.sublime-workspace

# OS-specific files
.DS_Store # macOS
Thumbs.db # Windows

# Logs
*.log

# Test and coverage
.coverage
htmlcov/

*/migrations/


<<<<<<< HEAD
.kiro/
=======
.kiro/
.DS_Store
>>>>>>> 226ec77 (kiro task 2)
\n# Env\n.env
\n# Production Env\n.env.production
\n# Frontend Env\nfrontend/.env\nfrontend/.env.production
frontend/build.zip
