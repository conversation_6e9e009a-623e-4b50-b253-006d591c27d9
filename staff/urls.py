from django.urls import include, path
from .views import *


urlpatterns = [
   path('dashboard/', StaffDashboardView.as_view(), name='staff_dashboard'),
   path('dashboard-stats/', StaffDashboardStatsView.as_view(), name='staff-dashboard-stats'),
   path('register-client/', RegisterClient.as_view(), name='register_client'),
   path('add-class/', ClientClassMethods.as_view(), name='add_class'),
   path('search-clients/', SearchClientDataForStaff.as_view(), name='search_clients_staff'),
   path('check-class-exists/', CheckClassExistsView.as_view(), name='check_class_exists'),
   path('get-client-classes/', GetClientClassesView.as_view(), name='get_client_classes'),
   path('upload/', FileUploadView.as_view(), name='file-upload'),
   path('download-document/<int:doc_id>/', DownloadDocumentView.as_view(), name='download-document'),
   path('documents/', StaffDocumentManagerView.as_view(), name='staff-documents'),
]

