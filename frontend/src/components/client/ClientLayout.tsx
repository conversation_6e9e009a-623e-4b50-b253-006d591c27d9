
import React, { useState } from 'react';
import { Box, Toolbar, useTheme, useMediaQuery, IconButton, AppBar, Typography, Button } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import ClientSidebar from './ClientSidebar';
import { useAuth } from '../../hooks/AuthContext';

const ClientLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user, logout } = useAuth();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Box sx={{ display: 'flex' }}>
        <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
            <Toolbar>
                {isMobile && (
                    <IconButton
                        color="inherit"
                        aria-label="open drawer"
                        edge="start"
                        onClick={handleDrawerToggle}
                        sx={{ mr: 2, display: { sm: 'none' } }}
                    >
                        <MenuIcon />
                    </IconButton>
                )}
                <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                    <img
                        src="/ampersandlogo.svg"
                        alt="Ampersand Capital Logo"
                        style={{
                            height: '32px',
                            width: 'auto',
                            marginRight: '12px'
                        }}
                    />
                    <Typography variant="h6" component="div">
                        Ampersand Capital
                    </Typography>
                </Box>
                {user && <Button color="inherit" onClick={logout}>Logout</Button>}
            </Toolbar>
        </AppBar>
      <ClientSidebar mobileOpen={mobileOpen} handleDrawerToggle={handleDrawerToggle} />
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

export default ClientLayout;
