import React, { useEffect, useState } from 'react';
import { getDocuments, downloadDocument } from '../../services/clientService';
import {
  Box, Typography, Accordion, AccordionSummary, AccordionDetails, List, ListItem, ListItemText, Button
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { saveAs } from 'file-saver';

const Statements: React.FC = () => {
  const [documents, setDocuments] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await getDocuments();
        setDocuments(data);
      } catch (error) {
        console.error('Failed to fetch documents', error);
      }
      setLoading(false);
    };

    fetchData();
  }, []);

  const handleDownload = async (doc: any) => {
    try {
      const blob = await downloadDocument(doc.id);
      saveAs(blob, doc.file_name);
    } catch (error) {
      console.error('Failed to download document', error);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        All Documents
      </Typography>

      <Typography variant="h5" gutterBottom>Company Documents</Typography>
      {documents && Object.entries(documents.company_documents).map(([year, quarters]) => (
        <Accordion key={year}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}><Typography>{year}</Typography></AccordionSummary>
          <AccordionDetails>
            {Object.entries(quarters as any).map(([quarter, months]) => (
              <Accordion key={quarter}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}><Typography>Quarter {quarter}</Typography></AccordionSummary>
                <AccordionDetails>
                  {Object.entries(months as any).map(([month, docs]) => (
                    <Box key={month} sx={{ mb: 2 }}>
                      <Typography variant="h6">{month}</Typography>
                      <List>
                        {(docs as any[]).map((doc: any) => (
                          <ListItem key={doc.id}>
                            <ListItemText primary={doc.subtype_display || doc.subtype} />
                            <Button variant="contained" onClick={() => handleDownload(doc)}>Download</Button>
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  ))}
                </AccordionDetails>
              </Accordion>
            ))}
          </AccordionDetails>
        </Accordion>
      ))}

      <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>Client Documents</Typography>
      {documents && Object.entries(documents.client_documents).map(([className, years]) => (
        <Accordion key={className}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}><Typography>Class {className}</Typography></AccordionSummary>
          <AccordionDetails>
            {Object.entries(years as any).map(([year, docs]) => (
              <Accordion key={year}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}><Typography>{year}</Typography></AccordionSummary>
                <AccordionDetails>
                  <List>
                    {(docs as any[]).map((doc: any) => (
                      <ListItem key={doc.id}>
                        <ListItemText primary={`Statement - ${doc.month}`} />
                        <Button variant="contained" onClick={() => handleDownload(doc)}>Download</Button>
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default Statements;