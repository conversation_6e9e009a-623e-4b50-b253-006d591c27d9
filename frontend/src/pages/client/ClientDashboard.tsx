import React, { useEffect, useState } from 'react';
import { getDashboardData } from '../../services/clientService';
import { Bar, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Box, Typography, Paper, List, ListItem, ListItemText } from '@mui/material';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

const ClientDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await getDashboardData();
        setDashboardData(data);
      } catch (error) {
        console.error('Failed to fetch dashboard data', error);
      }
      setLoading(false);
    };

    fetchData();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  const performanceChartData = {
    labels: dashboardData?.performance_chart_data.map((d: any) => d.period),
    datasets: [
      {
        label: 'Fund Performance',
        data: dashboardData?.performance_chart_data.map((d: any) => d.fund_performance),
        backgroundColor: 'rgba(13, 27, 75, 0.8)',
      },
      {
        label: 'Benchmark Performance',
        data: dashboardData?.performance_chart_data.map((d: any) => d.benchmark_performance),
        backgroundColor: 'rgba(212, 175, 55, 0.8)',
      },
    ],
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {dashboardData?.message}
      </Typography>

      {/* Main Dashboard Grid */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          md: 'repeat(2, 1fr)',
          lg: 'repeat(3, 1fr)'
        },
        gap: 3
      }}>
        {/* Fund Performance Chart */}
        <Paper sx={{
          p: 3,
          height: { xs: 380, sm: 430, md: 480 },
          display: 'flex',
          flexDirection: 'column',
          gridColumn: { xs: '1', md: '1 / -1', lg: '1 / 3' }
        }}>
          <Typography variant="h6" sx={{ mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
            Fund vs Benchmark Performance (%)
          </Typography>
          <Box sx={{
            height: { xs: 300, sm: 350, md: 400 },
            width: '100%',
            position: 'relative',
            flexGrow: 1
          }}>
            <Bar
              data={performanceChartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                    labels: {
                      font: {
                        size: 14
                      },
                      padding: 20
                    }
                  },
                  tooltip: {
                    titleFont: {
                      size: 14
                    },
                    bodyFont: {
                      size: 13
                    }
                  }
                },
                scales: {
                  x: {
                    ticks: {
                      font: {
                        size: 12
                      }
                    }
                  },
                  y: {
                    ticks: {
                      font: {
                        size: 12
                      }
                    }
                  }
                }
              }}
            />
          </Box>
        </Paper>

        {/* Documents Section - positioned strategically */}
        <Paper sx={{
          p: 3,
          height: { xs: 360, sm: 430, md: 480 },
          display: 'flex',
          flexDirection: 'column',
          gridColumn: { xs: '1', lg: '3' }
        }}>
          <Typography variant="h6" sx={{ mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
            Documents for Current Month
          </Typography>
          <Box sx={{
            flexGrow: 1,
            overflow: 'auto'
          }}>
            {(dashboardData?.company_documents_current_month?.length > 0 ||
              (dashboardData?.folio_data?.classes && Object.keys(dashboardData.folio_data.classes).length > 0)) ? (
              <List>
                {dashboardData?.company_documents_current_month.map((doc: any) => (
                  <ListItem key={doc.id} component="a" href={doc.uploaded_file_url} target="_blank">
                    <ListItemText primary={`${doc.subtype_display || doc.subtype} (${doc.year}-${doc.month})`} />
                  </ListItem>
                ))}
                {dashboardData?.folio_data?.classes && Object.entries(dashboardData.folio_data.classes).map(([className, docs]: [string, any]) => (
                  <React.Fragment key={className}>
                    <Typography variant="subtitle1" sx={{ pl: 2, fontWeight: 'bold' }}>Class {className}</Typography>
                    {docs.map((doc: any) => (
                      <ListItem key={doc.id} component="a" href={doc.uploaded_file_url} target="_blank" sx={{ pl: 4 }}>
                        <ListItemText primary={`Statement (${doc.year}-${doc.month})`} />
                      </ListItem>
                    ))}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: 'text.secondary'
              }}>
                <Typography variant="body1">
                  No documents available
                </Typography>
              </Box>
            )}
          </Box>
        </Paper>

        {/* NAV Charts per Class */}
        {dashboardData?.folio_data?.nav_data && Object.entries(dashboardData.folio_data.nav_data).map(([className, navData]: [string, any]) => {
          const labels = Object.keys(navData);
          const preTaxNav = labels.map(label => navData[label].pre_tax_nav);
          const postTaxNav = labels.map(label => navData[label].post_tax_nav);

          const chartData = {
            labels,
            datasets: [
              {
                label: 'Pre-tax NAV',
                data: preTaxNav,
                borderColor: 'rgba(13, 27, 75, 0.8)',
                backgroundColor: 'rgba(13, 27, 75, 0.1)',
                fill: false,
                tension: 0.1,
              },
              {
                label: 'Post-tax NAV',
                data: postTaxNav,
                borderColor: 'rgba(212, 175, 55, 0.8)',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                fill: false,
                tension: 0.1,
              },
            ],
          };

          return (
            <Paper key={className} sx={{
              p: 3,
              height: { xs: 360, sm: 430, md: 480 },
              display: 'flex',
              flexDirection: 'column'
            }}>
              <Typography variant="h6" sx={{ mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
                Class {className} NAV
              </Typography>
              <Box sx={{
                height: { xs: 280, sm: 350, md: 400 },
                width: '100%',
                position: 'relative',
                flexGrow: 1
              }}>
                <Line data={chartData} options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: true,
                      position: 'top',
                    },
                  },
                }} />
              </Box>
            </Paper>
          );
        })}
      </Box>
    </Box>
  );
};

export default ClientDashboard;