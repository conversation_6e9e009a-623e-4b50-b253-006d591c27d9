import React, { useEffect, useState } from 'react';
import { getDocuments, downloadDocument } from '../../services/staffService';
import {
  TextField, Button, Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Accordion, AccordionSummary, AccordionDetails, Grid
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { saveAs } from 'file-saver';

const DocumentManager: React.FC = () => {
  const [documents, setDocuments] = useState<any>(null);
  const [filters, setFilters] = useState({ year: new Date().getFullYear(), month: new Date().getMonth() + 1 });
  const [loading, setLoading] = useState(true);

  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const data = await getDocuments(filters);
      setDocuments(data);
    } catch (error) {
      console.error('Failed to fetch documents', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters({ ...filters, [name]: value });
  };

  const handleDownload = async (doc: any) => {
    try {
      const blob = await downloadDocument(doc.id);
      saveAs(blob, doc.file_name);
    } catch (error) {
      console.error('Failed to download document', error);
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Document Manager
      </Typography>

      <Grid container spacing={2} alignItems="center" sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField name="year" label="Year" type="number" value={filters.year} onChange={handleFilterChange} />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField name="month" label="Month" type="number" value={filters.month} onChange={handleFilterChange} />
        </Grid>
        <Grid size={{ xs: 12, sm: 12, md: 4 }}>
          <Button variant="contained" onClick={fetchDocuments}>Filter</Button>
        </Grid>
      </Grid>

      {loading ? <div>Loading...</div> : documents && (
        <Box>
          <Typography variant="h5" gutterBottom>Company Documents ({documents.total_documents.company})</Typography>
          <TableContainer component={Paper} sx={{ mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>File Name</TableCell>
                  <TableCell>Subtype</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell>Download</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {documents.company_documents.map((doc: any) => (
                  <TableRow key={doc.id}>
                    <TableCell>{doc.file_name}</TableCell>
                    <TableCell>{doc.subtype_display || doc.subtype}</TableCell>
                    <TableCell>{doc.year}-{doc.month}</TableCell>
                    <TableCell>{(doc.file_size / 1024).toFixed(2)} KB</TableCell>
                    <TableCell>
                      <Button variant="contained" onClick={() => handleDownload(doc)}>Download</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ mt: 4, mb: 2 }}>
            <Typography variant="h5" gutterBottom>Client Documents ({documents.total_documents.client})</Typography>
          </Box>
          {documents.client_documents.map((folioGroup: any) => (
            <Accordion key={folioGroup.folio_number}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Folio: {folioGroup.folio_number} - {folioGroup.client_name}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                {Object.entries(folioGroup.classes).map(([className, docs]: [string, any]) => (
                  <Accordion key={className}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Class: {className}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <TableContainer component={Paper}>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>File Name</TableCell>
                              <TableCell>Date</TableCell>
                              <TableCell>Size</TableCell>
                              <TableCell>Download</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {docs.map((doc: any) => (
                              <TableRow key={doc.id}>
                                <TableCell>{doc.file_name}</TableCell>
                                <TableCell>{doc.year}-{doc.month}</TableCell>
                                <TableCell>{(doc.file_size / 1024).toFixed(2)} KB</TableCell>
                                <TableCell>
                                  <Button variant="contained" onClick={() => handleDownload(doc)}>Download</Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}
    </Box>
  );
}

export default DocumentManager;
