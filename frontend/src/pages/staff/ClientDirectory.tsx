import React, { useState } from 'react';
import { searchClients, addClassToClient, terminateClientClass, checkClassExists } from '../../services/staffService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  TextField, Button, Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert,
  Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';

interface ClientSearchResult {
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    folio_number: string;
    classes: Array<{
        class_name__name: string;
        terminated: boolean;
    }>;
}

const ClientDirectory: React.FC = () => {
  const [query, setQuery] = useState('');
  const [searchType, setSearchType] = useState('name'); // 'name' or 'folio'
  const [results, setResults] = useState<ClientSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Dialog state for creating folio
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUsername, setSelectedUsername] = useState('');
  const [newClassName, setNewClassName] = useState('');
  const [folioLoading, setFolioLoading] = useState(false);
  const [terminateDialogOpen, setTerminateDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState('');

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const searchData = searchType === 'name' ? { name_query: query } : { folio_number_query: query };
      const data = await searchClients(searchData);
      setResults(data);
    } catch (err: any) {
      setError(getErrorMessage(err));
      setResults([]);
    }

    setLoading(false);
  };

  const handleAddClass = (username: string) => {
    setSelectedUsername(username);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedUsername('');
    setNewClassName('');
    setError(null);
  };

  const handleConfirmAddClass = async () => {
    if (!newClassName) {
      setError('Class name is required.');
      return;
    }

    const formattedClassName = newClassName.toUpperCase().replace(/-/g, ' ').replace(/ +/g, ' ');

    setFolioLoading(true);
    setError(null);

    try {
      const { exists } = await checkClassExists(formattedClassName);
      if (exists) {
        await addClassToClient(selectedUsername, formattedClassName);
        // Refresh the search results to show the new class
        const searchData = searchType === 'name' ? { name_query: query } : { folio_number_query: query };
        const data = await searchClients(searchData);
        setResults(data);
        handleDialogClose();
      } else {
        const confirmed = window.confirm(`Class "${formattedClassName}" does not exist. Do you want to create it?`);
        if (confirmed) {
          await addClassToClient(selectedUsername, formattedClassName);
          // Refresh the search results to show the new class
          const searchData = searchType === 'name' ? { name_query: query } : { folio_number_query: query };
          const data = await searchClients(searchData);
          setResults(data);
          handleDialogClose();
        }
      }
    } catch (err: any) {
      setError(getErrorMessage(err));
    }

    setFolioLoading(false);
  };

  const handleTerminateClass = (username: string) => {
    setSelectedUsername(username);
    setTerminateDialogOpen(true);
  };

  const handleTerminateDialogClose = () => {
    setTerminateDialogOpen(false);
    setSelectedUsername('');
    setSelectedClass('');
    setError(null);
  };

  const handleConfirmTerminateClass = async () => {
    if (!selectedClass) {
      setError('Please select a class to terminate.');
      return;
    }

    setFolioLoading(true);
    setError(null);

    try {
      await terminateClientClass(selectedUsername, selectedClass);
      // Refresh the search results to show the updated class status
      const searchData = searchType === 'name' ? { name_query: query } : { folio_number_query: query };
      const data = await searchClients(searchData);
      setResults(data);
      handleTerminateDialogClose();
    } catch (err: any) {
      setError(getErrorMessage(err));
    }

    setFolioLoading(false);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Client Directory
      </Typography>
      <Box component="form" onSubmit={handleSearch} sx={{ mb: 4, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          label={`Search by ${searchType === 'name' ? 'Name' : 'Folio Number'}`}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          variant="outlined"
          sx={{ flexGrow: 1 }}
        />
        <Button onClick={() => setSearchType(searchType === 'name' ? 'folio' : 'name')}>
          Search by {searchType === 'name' ? 'Folio' : 'Name'}
        </Button>
        <Button type="submit" variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={24} /> : 'Search'}
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box sx={{ overflowX: 'auto' }}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Folio Number</TableCell>
                <TableCell>Username</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Classes</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {results.map((client) => (
                <TableRow key={client.username}>
                  <TableCell>{client.folio_number}</TableCell>
                  <TableCell>{client.username}</TableCell>
                  <TableCell>{`${client.first_name} ${client.last_name}`}</TableCell>
                  <TableCell>{client.email}</TableCell>
                  <TableCell>
                    {client.classes.map((c, index) => (
                      <span key={c.class_name__name} style={{ color: c.terminated ? 'red' : 'green' }}>
                        {c.class_name__name}{c.terminated ? ' (Terminated)' : ''}
                        {index < client.classes.length - 1 ? ', ' : ''}
                      </span>
                    ))}
                  </TableCell>
                  <TableCell>
                    <Button variant="contained" onClick={() => handleAddClass(client.username)}>Add Class</Button>
                    <Button variant="contained" color="secondary" onClick={() => handleTerminateClass(client.username)}>Terminate Class</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Add Class Dialog */}
      <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Class</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Adding a new class for user: <strong>{selectedUsername}</strong>
          </Typography>

          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <TextField
            label="Class Name"
            value={newClassName}
            onChange={(e) => setNewClassName(e.target.value)}
            fullWidth
            required
            helperText="Enter a class name. Hyphens will be replaced with spaces."
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} disabled={folioLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmAddClass}
            variant="contained"
            disabled={folioLoading || !newClassName}
          >
            {folioLoading ? <CircularProgress size={20} /> : 'Add Class'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Terminate Class Dialog */}
      <Dialog open={terminateDialogOpen} onClose={handleTerminateDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Terminate Class</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Terminating a class for user: <strong>{selectedUsername}</strong>
          </Typography>

          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <FormControl fullWidth>
            <InputLabel>Class to Terminate</InputLabel>
            <Select
              value={selectedClass}
              onChange={(e) => setSelectedClass(e.target.value)}
            >
              {results.find(client => client.username === selectedUsername)?.classes.map(classItem => (
                <MenuItem key={classItem.class_name__name} value={classItem.class_name__name}>{classItem.class_name__name}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleTerminateDialogClose} disabled={folioLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmTerminateClass}
            variant="contained"
            color="secondary"
            disabled={folioLoading || !selectedClass}
          >
            {folioLoading ? <CircularProgress size={20} /> : 'Terminate Class'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};


export default ClientDirectory;
