import datetime
from django.db import models
from django.contrib.auth.models import AbstractUser, UserManager
import os
from datetime import date
# accounts/models.py

def get_upload_path(instance, filename):
    def __str__(self):
        return f"{self.document_type} - {os.path.basename(self.uploaded_file.name)}"

    """
    Generates a dynamic upload path based on the document type.
    Example for Company: company/monthly_fund_performance/2025/07/report.pdf
    Example for Client: client/1001/2025/07/statement.pdf
    """
    today = date.today()
    year = instance.year or today.year
    month = instance.month or today.month

    if instance.document_type == 'Company':
        path = os.path.join(
            'company',
            instance.subtype,
            str(year),
            f'{month:02d}',
            filename
        )
    elif instance.document_type == 'Client':
        path = os.path.join(
            'client',
            str(instance.client.folio_number),
            str(year),
            f'{month:02d}',
            filename
        )
    else:
        # Fallback path
        path = os.path.join('uploads', str(year), f'{month:02d}', filename)
        
    return path

class User(AbstractUser):
    ROLE_CHOICES = (
        ('admin', 'Admin'),
        ('staff', 'Staff'),
        ('client', 'Client'),
    )
    username = models.CharField(max_length=150, unique=True)
    email = models.EmailField(unique=True)
    role = models.CharField(max_length=10, choices=ROLE_CHOICES)
    password = models.CharField(max_length=128)

    REQUIRED_FIELDS = ['role', 'password']

    objects = UserManager()

    def __str__(self):
        return f"{self.username} ({self.role})"
    
class Client(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='client')
    pan = models.CharField(max_length=10, unique=True)
    folio_number = models.CharField(max_length=255, unique=True, null=False)
    pw_reset = models.BooleanField(default=False) 

    def __str__(self):
        return f"Client {self.user.username}"
    
class Staff(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='staff')
    pw_reset = models.BooleanField(default=False) 

class Admin(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='admin')

class Class(models.Model):
    name = models.CharField(max_length=255, unique=True, null=False)

    def __str__(self):
        return self.name

class Folio(models.Model):
   client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='folios')
   class_name = models.ForeignKey(Class, on_delete=models.CASCADE, related_name='folios')
   terminated = models.BooleanField(default=False)
   created_at = models.DateTimeField(auto_now_add=True)
   nav_data = models.JSONField(default=dict)

   class Meta:
       unique_together = ('client', 'class_name')

   def __str__(self):
       return f"Folio for Client {self.client.user.username} with class {self.class_name.name} (Terminated: {self.terminated})"

class SystemMeta(models.Model):
    client_count = models.IntegerField(default=int(0))
    folio_count = models.IntegerField(default=int(0))

class Document(models.Model):
    class DocumentType(models.TextChoices):
        COMPANY = 'Company', 'Company'
        CLIENT = 'Client', 'Client'

    class CompanySubtype(models.TextChoices):
        FUND_PERFORMANCE = 'monthly_fund_performance', 'Monthly Fund Performance'
        INVESTOR_PRESENTATION = 'monthly_investor_presentation', 'Monthly Investor Presentation'
        SEBI_REPORT = 'sebi_quarterly_report', 'SEBI Quarterly Report'
    
    # Core Fields
    document_type = models.CharField(max_length=10, choices=DocumentType.choices)
    uploaded_file = models.FileField(upload_to=get_upload_path)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated_at = models.DateTimeField(auto_now=True)
    
    # Company-specific fields
    subtype = models.CharField(max_length=50, choices=CompanySubtype.choices, null=True, blank=True)

    # Client-specific fields
    client = models.ForeignKey(Client, on_delete=models.SET_NULL, null=True, blank=True, related_name='documents')
    folio = models.ForeignKey(Folio, on_delete=models.SET_NULL, null=True, blank=True, related_name='documents')
    year = models.PositiveIntegerField(null=True, blank=True)
    month = models.PositiveIntegerField(null=True, blank=True)

# In accounts/models.py (add these new models)

class FundNAVHistory(models.Model):
    """Stores the historical NAV of the fund on a given date."""
    date = models.DateField(unique=True, db_index=True)
    nav = models.DecimalField(max_digits=14, decimal_places=4)

    class Meta:
        ordering = ['date']

    def __str__(self):
        return f"Fund NAV on {self.date}: {self.nav}"

class BenchmarkHistory(models.Model):
    """Stores the historical value of the benchmark index."""
    date = models.DateField(unique=True, db_index=True)
    value = models.DecimalField(max_digits=14, decimal_places=4)

    class Meta:
        ordering = ['date']

    def __str__(self):
        return f"Benchmark on {self.date}: {self.value}"