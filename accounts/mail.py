import os
import json
import base64
import logging
from email.mime.text import MI<PERSON><PERSON>ex<PERSON>
from email.mime.multipart import MI<PERSON><PERSON>ultipart

from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/gmail.send"]
TOKEN_FILE = os.path.join(os.path.dirname(__file__), 'token.json')


def get_gmail_service():
    """
    Get authenticated Gmail service using OAuth credentials.
    """
    creds = None

    # Load existing token
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)

    # If there are no (valid) credentials available, log error
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
                # Save the refreshed credentials back to the file
                with open(TOKEN_FILE, 'w') as token:
                    token.write(creds.to_json())
                logger.info("OAuth token refreshed successfully")
            except Exception as e:
                logger.error(f"Failed to refresh OAuth token: {str(e)}")
                raise Exception("OAuth credentials expired and could not be refreshed")
        else:
            logger.error("No valid OAuth credentials found")
            raise Exception("No valid OAuth credentials available")

    try:
        service = build('gmail', 'v1', credentials=creds)
        return service
    except Exception as e:
        logger.error(f"Failed to build Gmail service: {str(e)}")
        raise


def create_message(sender, to, subject, message_text):
    """
    Create a message for an email.
    """
    message = MIMEText(message_text)
    message['to'] = to
    message['from'] = sender
    message['subject'] = subject

    # Encode the message
    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
    return {'raw': raw_message}


def send_gmail_message(service, user_id, message):
    """
    Send an email message using Gmail API.
    """
    try:
        message = service.users().messages().send(userId=user_id, body=message).execute()
        logger.info(f"Email sent successfully. Message ID: {message['id']}")
        return message
    except HttpError as error:
        logger.error(f"An error occurred while sending email: {error}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error while sending email: {str(e)}")
        raise

def send_mail(type, user):
    """
    Send email using Gmail API with OAuth authentication.
    """
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))

    # Construct the full URL for the frontend
    reset_url = f"https://{settings.FRONTEND_URL}/password-reset/confirm/{uidb64}/{token}/"
    to_email = user.email

    # Get sender email from settings or use a default
    sender_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')

    if type == 'reset':
        body = f"""Hi {user.first_name},

You requested a password reset. Please click the link below to reset your password:

{reset_url}

If you did not request this, please ignore this email.

Best regards,
Ampersand Capital Team"""
        subject = "Password Reset Request"

    elif type == 'welcome':
        body = f"""Hi {user.first_name},

Your account has been created successfully. Your login credentials are:

Username: {user.username}
Password reset link: {reset_url}
Login link: https://{settings.FRONTEND_URL}

You will be prompted to set a new password upon your first login. Once set, you can log in using the provided username and your password.

Best regards,
Ampersand Capital Team"""
        subject = "Welcome to Ampersand Capital"
    else:
        logger.error(f"Unknown email type: {type}")
        return False

    try:
        # Get Gmail service
        service = get_gmail_service()

        # Create message
        message = create_message(sender_email, to_email, subject, body)

        # Send message
        result = send_gmail_message(service, 'me', message)

        logger.info(f"Email sent successfully to {to_email}. Type: {type}")
        print(f"Email sent successfully to {to_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email to {to_email}. Error: {str(e)}")
        print(f"Failed to send email: {str(e)}")

        # Fallback to Django's SMTP if OAuth fails (optional)
        try:
            from django.core.mail import send_mail as django_send_mail
            django_send_mail(
                subject,
                body,
                sender_email,
                [to_email],
                fail_silently=False,
            )
            logger.info(f"Email sent via SMTP fallback to {to_email}")
            print("Email sent via SMTP fallback")
            return True
        except Exception as smtp_error:
            logger.error(f"SMTP fallback also failed: {str(smtp_error)}")
            print(f"Both OAuth and SMTP failed: {str(smtp_error)}")
            return False
